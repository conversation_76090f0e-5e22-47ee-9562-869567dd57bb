import { useRouter } from 'next/router'
import { useCallback, useState, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import configuration from '~/configuration'
import { IParamsTableInfinity } from '~/core/@types/global'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import useContextGraphQL, {
  IResponseContextResult
} from '~/core/middleware/use-context-graphQL'
import { IPromiseSearchOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import MutationImportJobs from '~/lib/features/settings/import/graphql/mutation-import-jobs'
import MutationImportProfiles from '~/lib/features/settings/import/graphql/mutation-import-profiles'
import MutationImportTenantCourses from '~/lib/features/settings/import/graphql/mutation-import-tenant-courses'
import QueryImportedJobHistory from '~/lib/features/settings/import/graphql/query-import-file-history'
import QueryImportedProfileHistory from '~/lib/features/settings/import/graphql/query-import-file-history-profiles'
import QueryImportedTenantCourseHistory from '~/lib/features/settings/import/graphql/query-import-file-history-tenant-courses'
import QueryJobFields from '~/lib/features/settings/import/graphql/query-job-fields'
import QueryJobImportFilesList from '~/lib/features/settings/import/graphql/query-job-import-files-list'
import QueryProfileFields from '~/lib/features/settings/import/graphql/query-profile-fields'
import QueryProfileImportFilesList from '~/lib/features/settings/import/graphql/query-profile-import-files-list'
import QueryTenantCourseFields from '~/lib/features/settings/import/graphql/query-tenant-course-fields'
import QueryTenantCourseImportFilesList from '~/lib/features/settings/import/graphql/query-tenant-course-import-files-list'
import {
  ImportFileCoursesHistoriesType,
  ImportFileHistoriesType,
  ImportFileProfilesHistoriesType
} from '~/lib/features/settings/import/types'
import { IPageResult } from '~/lib/hooks/use-infinity-query-search'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import { JobFieldType, MappedFieldType, ProfileFieldType } from '../types'
import useLoadingBlock from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'
import { ImportTypeKey } from '~/lib/features/settings/import/types'

const useMappingFields = ({
  mappedFields,
  file
}: {
  mappedFields?: MappedFieldType[]
  file?: File
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlock()
  const { clientGraphQL } = useContextGraphQL()
  const clientRequest = useGraphQLRequest({ language: user?.language })

  const [isProcessing, setIsProcessing] = useState<boolean>(false)

  const { trigger: importJobs } = useSubmitCommon(MutationImportJobs)
  const { trigger: importCandidates } = useSubmitCommon(MutationImportProfiles)
  const { trigger: importCourses } = useSubmitCommon(
    MutationImportTenantCourses
  )

  const transformFieldData = useCallback((collection: any[]) => {
    return collection.map((item) => ({
      value: item.uuid,
      supportingObj: {
        name: item.name,
        nameRequired: item.required
      }
    }))
  }, [])

  const handleImportError = useCallback(
    (error: any): Promise<boolean> => {
      setCloseLockApp()
      setIsProcessing(false)

      const errorMessage = error.graphQLErrors?.[0]?.message || ''

      if (errorMessage.includes('duplicated')) {
        setToast({
          open: true,
          type: 'error',
          title: `${t('notification:duplicated_title')}`,
          description: errorMessage
        })
      } else if (errorMessage.includes('required fields')) {
        setToast({
          open: true,
          type: 'error',
          title: `${t('notification:import_mapping_require_title')}`,
          description: errorMessage
        })
      } else {
        catchErrorFromGraphQL({ error, setToast, router })
      }

      return Promise.resolve(false)
    },
    [setCloseLockApp, setToast, router, t]
  )

  const promiseJobFields = useCallback(
    (params = {} as IPromiseSearchOption) =>
      clientGraphQL
        .query(QueryJobFields, { ...params, mappedFields })
        .toPromise()
        .then((result: IResponseContextResult<JobFieldType>) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { jobFieldsList } = result.data
          const collection = jobFieldsList?.collection || []
          const metadata = jobFieldsList?.metadata || {}

          return {
            metadata,
            collection: transformFieldData(collection)
          }
        }),
    [clientGraphQL, mappedFields, transformFieldData]
  )

  const promiseProfileFields = useCallback(
    (params = {} as IPromiseSearchOption) =>
      clientGraphQL
        .query(QueryProfileFields, { ...params, mappedFields })
        .toPromise()
        .then((result: IResponseContextResult<ProfileFieldType>) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { profileFieldsList } = result.data
          const collection = profileFieldsList?.collection || []
          const metadata = profileFieldsList?.metadata || {}

          return {
            metadata,
            collection: transformFieldData(collection)
          }
        }),
    [clientGraphQL, mappedFields, transformFieldData]
  )

  const promiseCourseFields = useCallback(
    (params = {} as IPromiseSearchOption) =>
      clientGraphQL
        .query(QueryTenantCourseFields, { ...params, mappedFields })
        .toPromise()
        .then((result: IResponseContextResult<ProfileFieldType>) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { tenantCourseFieldsList } = result.data
          const collection = tenantCourseFieldsList?.collection || []
          const metadata = tenantCourseFieldsList?.metadata || {}

          return {
            metadata,
            collection: transformFieldData(collection)
          }
        }),
    [clientGraphQL, mappedFields, transformFieldData]
  )

  const onImportJobs = useCallback(
    (data: { mappedFields: MappedFieldType[] }): Promise<any> => {
      setIsProcessing(true)
      setShowLockApp('')
      return importJobs({
        file,
        mappedFields: data?.mappedFields
      }).then((result) => {
        if (result.error) {
          return handleImportError(result.error)
        }

        if (result.data.jobsImport?.import.id) {
          setCloseLockApp()
          return router.push(
            `${configuration.path.settings.import}?import_id=${result.data.jobsImport?.import.id}&object_kind=job`
          )
        }

        return Promise.resolve(true)
      })
    },
    [
      file,
      importJobs,
      handleImportError,
      setShowLockApp,
      setCloseLockApp,
      router
    ]
  )

  const onImportCandidates = useCallback(
    (data: { mappedFields: MappedFieldType[] }): Promise<any> => {
      setIsProcessing(true)
      setShowLockApp('')
      return importCandidates({
        file,
        mappedFields: data?.mappedFields
      }).then((result) => {
        if (result.error) {
          return handleImportError(result.error)
        }

        if (result.data.profilesImport?.import.id) {
          setCloseLockApp()
          return router.push(
            `${configuration.path.settings.import}?import_id=${result.data.profilesImport?.import.id}&object_kind=profile`
          )
        }

        return Promise.resolve(true)
      })
    },
    [
      file,
      importCandidates,
      handleImportError,
      setShowLockApp,
      setCloseLockApp,
      router
    ]
  )

  const onImportCourse = useCallback(
    (data: { mappedFields: MappedFieldType[] }): Promise<any> => {
      setIsProcessing(true)
      setShowLockApp('')
      return importCourses({
        file,
        mappedFields: data?.mappedFields
      }).then((result) => {
        if (result.error) {
          return handleImportError(result.error)
        }

        if (result.data.tenantCoursesImport?.import.id) {
          setCloseLockApp()
          return router.push(
            `${configuration.path.settings.import}?import_id=${result.data.tenantCoursesImport?.import.id}&object_kind=tenant_course`
          )
        }

        return Promise.resolve(true)
      })
    },
    [
      file,
      importCourses,
      handleImportError,
      setShowLockApp,
      setCloseLockApp,
      router
    ]
  )

  // Original history functions - GIỮ NGUYÊN LOGIC CŨ
  const getJobsImportHistory = useCallback(
    async (pageParam = {} as IParamsTableInfinity) => {
      const { page } = pageParam
      return clientRequest
        .query(QueryJobImportFilesList, {
          page,
          limit: configuration.defaultPageSize
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.candidates.list,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const collection = result?.data?.jobImportsList?.collection || []
          const metadata = result?.data?.jobImportsList?.metadata

          return {
            data: collection,
            meta: {
              totalRowCount: metadata?.totalCount || 0,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    [clientRequest, setToast]
  )

  const getProfilesImportHistory = useCallback(
    async (pageParam = {} as IParamsTableInfinity) => {
      const { page } = pageParam
      return clientRequest
        .query(QueryProfileImportFilesList, {
          page,
          limit: configuration.defaultPageSize
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.candidates.list,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const collection = result?.data?.profileImportsList?.collection || []
          const metadata = result?.data?.profileImportsList?.metadata

          return {
            data: collection,
            meta: {
              totalRowCount: metadata?.totalCount || 0,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    [clientRequest, setToast]
  )

  const getTenantCoursesImportHistory = useCallback(
    async (pageParam = {} as IParamsTableInfinity) => {
      const { page } = pageParam
      return clientRequest
        .query(QueryTenantCourseImportFilesList, {
          page,
          limit: configuration.defaultPageSize
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.candidates.list,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const collection =
            result?.data?.tenantCourseImportsList?.collection || []
          const metadata = result?.data?.tenantCourseImportsList?.metadata

          return {
            data: collection,
            meta: {
              totalRowCount: metadata?.totalCount || 0,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    [clientRequest, setToast]
  )

  const getImportedJobsHistory = useCallback(
    async (
      pageParam = {} as IParamsTableInfinity
    ): Promise<IPageResult<ImportFileHistoriesType>> => {
      return clientGraphQL
        .query(QueryImportedJobHistory, {
          ...pageParam,
          limit: configuration.defaultPageSize,
          importId: Number(router.query['import_id'])
        })
        .toPromise()
        .then((result: IResponseContextResult<ImportFileHistoriesType>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.settings.departments,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const { jobImportHitoriesList } = result.data
          const collection = (jobImportHitoriesList?.collection || []).map(
            (item) => ({ ...item, id: item.data.key })
          )
          const metadata = jobImportHitoriesList?.metadata || {}

          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    [clientGraphQL, router.query, setToast]
  )

  const getImportedProfilesHistory = useCallback(
    async (
      pageParam = {} as IParamsTableInfinity
    ): Promise<IPageResult<ImportFileProfilesHistoriesType>> => {
      return clientGraphQL
        .query(QueryImportedProfileHistory, {
          ...pageParam,
          limit: configuration.defaultPageSize,
          importId: Number(router.query['import_id'])
        })
        .toPromise()
        .then(
          (result: IResponseContextResult<ImportFileProfilesHistoriesType>) => {
            if (result.error) {
              catchErrorFromGraphQL({
                error: result.error,
                page: configuration.path.settings.departments,
                setToast
              })

              return {
                data: [],
                meta: {
                  totalRowCount: 0,
                  pageSize: configuration.defaultPageSize
                }
              }
            }

            const { profileImportHitoriesList } = result.data
            const collection = (
              profileImportHitoriesList?.collection || []
            ).map((item) => ({ ...item, id: item.data.key }))
            const metadata = profileImportHitoriesList?.metadata || {}

            return {
              data: collection,
              meta: {
                totalRowCount: metadata.totalCount,
                pageSize: configuration.defaultPageSize
              }
            }
          }
        )
    },
    [clientGraphQL, router.query, setToast]
  )

  const getImportedTenantCoursesHistory = useCallback(
    async (
      pageParam = {} as IParamsTableInfinity
    ): Promise<IPageResult<ImportFileCoursesHistoriesType>> => {
      return clientGraphQL
        .query(QueryImportedTenantCourseHistory, {
          ...pageParam,
          limit: configuration.defaultPageSize,
          importId: Number(router.query['import_id'])
        })
        .toPromise()
        .then(
          (result: IResponseContextResult<ImportFileCoursesHistoriesType>) => {
            if (result.error) {
              catchErrorFromGraphQL({
                error: result.error,
                page: configuration.path.settings.departments,
                setToast
              })

              return {
                data: [],
                meta: {
                  totalRowCount: 0,
                  pageSize: configuration.defaultPageSize
                }
              }
            }

            const { tenantCourseImportHistoriesList } = result.data
            const collection = (
              tenantCourseImportHistoriesList?.collection || []
            ).map((item) => ({ ...item, id: item.data.key }))
            const metadata = tenantCourseImportHistoriesList?.metadata || {}

            return {
              data: collection,
              meta: {
                totalRowCount: metadata.totalCount,
                pageSize: configuration.defaultPageSize
              }
            }
          }
        )
    },
    [clientGraphQL, router.query, setToast]
  )

  return {
    promiseJobFields,
    promiseProfileFields,
    promiseCourseFields,
    isProcessing,
    onImportJobs,
    onImportCandidates,
    onImportCourse,
    getJobsImportHistory,
    getImportedJobsHistory,
    getProfilesImportHistory,
    getImportedProfilesHistory,
    getTenantCoursesImportHistory,
    getImportedTenantCoursesHistory
  }
}

export const useImportHandler = (
  importType: { value: ImportTypeKey },
  mappedFields?: MappedFieldType[],
  file?: File
) => {
  const {
    promiseJobFields,
    promiseProfileFields,
    promiseCourseFields,
    onImportJobs,
    onImportCandidates,
    onImportCourse,
    getJobsImportHistory,
    getImportedJobsHistory,
    getProfilesImportHistory,
    getImportedProfilesHistory,
    getTenantCoursesImportHistory,
    getImportedTenantCoursesHistory,
    isProcessing
  } = useMappingFields({ mappedFields, file })

  const handlerMaps = {
    promiseFields: {
      jobs: promiseJobFields,
      candidate: promiseProfileFields,
      course: promiseCourseFields
    },
    onImport: {
      jobs: onImportJobs,
      candidate: onImportCandidates,
      course: onImportCourse
    },
    getImportHistory: {
      jobs: getJobsImportHistory,
      candidate: getProfilesImportHistory,
      course: getTenantCoursesImportHistory
    },
    getImportedHistory: {
      jobs: getImportedJobsHistory,
      candidate: getImportedProfilesHistory,
      course: getImportedTenantCoursesHistory
    }
  } as 

  const getHandler = <K extends keyof typeof handlerMaps>(
    mapKey: K,
    type: ImportTypeKey
  ) => {
    const map = handlerMaps[mapKey]
    return map[type] || map.course
  }

  return {
    promiseFields: (params?: IPromiseSearchOption) =>
      getHandler('promiseFields', importType.value)(params),

    onImport: (data: { mappedFields: MappedFieldType[] }) =>
      getHandler('onImport', importType.value)(data),

    getImportHistory: (pageParam?: IParamsTableInfinity) =>
      getHandler('getImportHistory', importType.value)(pageParam),

    getImportedHistory: (pageParam?: IParamsTableInfinity) =>
      getHandler('getImportedHistory', importType.value)(pageParam),

    isProcessing,
    currentType: importType.value
  }
}

export default useImportHandler
