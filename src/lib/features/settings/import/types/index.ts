import { ILogoAndAvatarVariants } from '~/core/@types/global'
import { ENUMS_IMPORT_TYPE } from './utilities/enum'

export type MappedFieldType = {
  fileField: string
  mappedUuid: string | null
  name: string | null
  required: boolean
}

type ImportFieldType = {
  field: string
  name: string
  required: boolean
  uuid: string
}

export type ImportTypeKey = keyof typeof ENUMS_IMPORT_TYPE

export type JobFieldType = Partial<ImportFieldType>

export type ProfileFieldType = Partial<ImportFieldType>

export type CourseFieldType = Partial<ImportFieldType>

type ImportFieldData = {
  column: string
  row: number
  key: string
  description: string
  message: string
}

type ImportHistoryEntry<ImportFieldData> = {
  id: string
  data: ImportFieldData
  rawData: Record<string, string>
  grouppedKey: string
  grouppedKeyDescription: string
}

export type ImportFileHistoriesType = ImportHistoryEntry<
  ImportFieldData & {
    jobId: number
    publicJobId: number
  }
>

export type ImportFileProfilesHistoriesType = ImportHistoryEntry<
  ImportFieldData & {
    profileId: number
  }
>

export type ImportFileCoursesHistoriesType = ImportHistoryEntry<
  ImportFieldData & {
    tenantCourseId: number
  }
>

export type ImportFileCompaniesHistoriesType = ImportHistoryEntry<
  ImportFieldData & {
    companyId: number
  }
>

export type ImportFileStatus =
  | 'in_progress'
  | 'failed'
  | 'completed'
  | 'partial'

export type ImportFileType = {
  id: string
  name: string
  file: string
  uploadedBy: {
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }
  importedCount: number
  rowsCount: number
  status: ImportFileStatus
  statusDescription: string
  createdAt: string
  objectKindDescription: string
}

export type ISourceImportType = {
  value: string
  text: string
  description: string
  key: boolean
  templateUrl: {
    direct: string
    agency: string
  }
}

export type IResponseKeysImport = {
  fieldsListKey: string
  importKey: string
}

export type GenericFieldType = {
  uuid: string
  name: string
  required: boolean
}
